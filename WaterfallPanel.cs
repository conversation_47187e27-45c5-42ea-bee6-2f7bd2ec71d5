using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace LiveCAN
{
    public partial class WaterfallPanel : UserControl
    {
        private const int WaterfallHeight = 100; // Number of horizontal lines
        private const int LineHeight = 2; // Height of each waterfall line
        private const int MessageWidth = 4; // Width of each message block
        private const int ScrollSpeed = 1; // Pixels to scroll per update
        
        private Timer updateTimer;
        private readonly List<WaterfallLine> waterfallLines = new List<WaterfallLine>();
        private readonly Dictionary<string, Color> frameIdColors = new Dictionary<string, Color>();
        private readonly Random colorRandom = new Random();
        private int scrollOffset = 0;
        private bool isDisposed = false;

        // Performance optimizations
        private readonly SolidBrush[] colorBrushCache = new SolidBrush[256];
        private readonly Color[] blueGradientCache = new Color[101]; // 0.0 to 1.0 in 0.01 increments
        private bool needsFullRedraw = true;

        public WaterfallPanel()
        {
            InitializeComponent();
            
            // Initialize waterfall lines
            for (int i = 0; i < WaterfallHeight; i++)
            {
                waterfallLines.Add(new WaterfallLine());
            }
            
            // Initialize color caches
            InitializeBlueGradientCache();
            InitializeBrushCache();
        }

        private void InitializeBlueGradientCache()
        {
            // Create blue gradient from light blue to dark blue
            for (int i = 0; i <= 100; i++)
            {
                float intensity = i / 100f;
                int r = (int)(173 - (53 * intensity));  // 173 -> 120
                int g = (int)(216 - (66 * intensity));  // 216 -> 150  
                int b = (int)(255 - (135 * intensity)); // 255 -> 120
                blueGradientCache[i] = Color.FromArgb(255, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
            }
        }

        private void InitializeBrushCache()
        {
            // Pre-create brushes for different colors
            for (int i = 0; i < 256; i++)
            {
                colorBrushCache[i] = new SolidBrush(Color.FromArgb(i, 100, 150, 255));
            }
        }

        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            // Check if disposed to prevent errors during shutdown
            if (isDisposed || IsDisposed)
                return;

            // Scroll the waterfall
            scrollOffset += ScrollSpeed;
            if (scrollOffset >= Width + MessageWidth)
            {
                scrollOffset = 0;
            }

            // Age all messages and remove old ones
            bool hasChanges = false;
            foreach (var line in waterfallLines)
            {
                for (int i = line.Messages.Count - 1; i >= 0; i--)
                {
                    var msg = line.Messages[i];
                    msg.X -= ScrollSpeed;
                    
                    // Remove messages that have scrolled off screen
                    if (msg.X + MessageWidth < 0)
                    {
                        line.Messages.RemoveAt(i);
                        hasChanges = true;
                    }
                }
            }

            if (hasChanges || needsFullRedraw)
            {
                Invalidate();
                needsFullRedraw = false;
            }
        }

        public void BlipMessage(string frameId)
        {
            if (isDisposed || IsDisposed)
                return;

            // Get or create color for this frame ID
            if (!frameIdColors.ContainsKey(frameId))
            {
                // Generate a blue-tinted color for consistency with other panels
                float hue = (frameId.GetHashCode() % 360 + 360) % 360;
                Color baseColor = ColorFromHSV(hue, 0.7f, 0.9f);
                
                // Tint towards blue to match other panels
                int r = (baseColor.R + 100) / 2;
                int g = (baseColor.G + 150) / 2;
                int b = Math.Min(255, (baseColor.B + 255) / 2);
                
                frameIdColors[frameId] = Color.FromArgb(255, r, g, b);
            }

            // Find the best line to add this message (least crowded)
            int bestLine = 0;
            int minMessages = int.MaxValue;
            
            for (int i = 0; i < waterfallLines.Count; i++)
            {
                int messageCount = waterfallLines[i].Messages.Count;
                if (messageCount < minMessages)
                {
                    minMessages = messageCount;
                    bestLine = i;
                }
            }

            // Add message to the selected line
            var message = new WaterfallMessage
            {
                FrameId = frameId,
                Color = frameIdColors[frameId],
                X = Width, // Start from right edge
                Intensity = 1.0f,
                CreationTime = Environment.TickCount
            };

            waterfallLines[bestLine].Messages.Add(message);
            needsFullRedraw = true;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (isDisposed || IsDisposed)
                return;

            base.OnPaint(e);
            
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.CompositingQuality = CompositingQuality.HighSpeed;

            // Clear background
            g.Clear(BackColor);

            // Draw waterfall lines
            for (int lineIndex = 0; lineIndex < waterfallLines.Count; lineIndex++)
            {
                int y = lineIndex * LineHeight;
                var line = waterfallLines[lineIndex];

                foreach (var message in line.Messages)
                {
                    // Calculate fade based on age
                    int age = Environment.TickCount - message.CreationTime;
                    float fade = Math.Max(0f, 1f - (age / 5000f)); // Fade over 5 seconds
                    
                    if (fade > 0)
                    {
                        // Create faded color
                        int alpha = (int)(255 * fade * message.Intensity);
                        Color fadedColor = Color.FromArgb(alpha, message.Color);
                        
                        // Get cached brush or create new one
                        using (var brush = new SolidBrush(fadedColor))
                        {
                            g.FillRectangle(brush, message.X, y, MessageWidth, LineHeight);
                        }
                    }
                }
            }

            // Draw subtle grid lines for better visibility
            using (var gridPen = new Pen(Color.FromArgb(30, Color.White), 1))
            {
                // Horizontal lines every 10 rows
                for (int i = 0; i < WaterfallHeight; i += 10)
                {
                    int y = i * LineHeight;
                    g.DrawLine(gridPen, 0, y, Width, y);
                }
            }
        }

        private Color ColorFromHSV(float hue, float saturation, float value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            float f = (float)(hue / 60 - Math.Floor(hue / 60));

            value = value * 255;
            int v = Convert.ToInt32(value);
            int p = Convert.ToInt32(value * (1 - saturation));
            int q = Convert.ToInt32(value * (1 - f * saturation));
            int t = Convert.ToInt32(value * (1 - (1 - f) * saturation));

            if (hi == 0)
                return Color.FromArgb(255, v, t, p);
            else if (hi == 1)
                return Color.FromArgb(255, q, v, p);
            else if (hi == 2)
                return Color.FromArgb(255, p, v, t);
            else if (hi == 3)
                return Color.FromArgb(255, p, q, v);
            else if (hi == 4)
                return Color.FromArgb(255, t, p, v);
            else
                return Color.FromArgb(255, v, p, q);
        }

        private class WaterfallLine
        {
            public List<WaterfallMessage> Messages { get; } = new List<WaterfallMessage>();
        }

        private class WaterfallMessage
        {
            public string FrameId { get; set; }
            public Color Color { get; set; }
            public int X { get; set; }
            public float Intensity { get; set; }
            public int CreationTime { get; set; }
        }
    }
}
