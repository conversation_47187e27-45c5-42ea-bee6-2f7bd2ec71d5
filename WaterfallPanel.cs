using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace LiveCAN
{
    public partial class WaterfallPanel : UserControl
    {
        private const int WaterfallWidth = 200; // Number of vertical columns
        private const int ColumnWidth = 3; // Width of each waterfall column
        private const int MessageHeight = 4; // Height of each message block
        private const int ScrollSpeed = 2; // Pixels to scroll per update

        private Timer updateTimer;
        private readonly List<WaterfallColumn> waterfallColumns = new List<WaterfallColumn>();
        private readonly Dictionary<string, Color> frameIdColors = new Dictionary<string, Color>();
        private readonly Random colorRandom = new Random();
        private int scrollOffset = 0;
        private bool isDisposed = false;

        // Performance optimizations
        private readonly SolidBrush[] colorBrushCache = new SolidBrush[256];
        private readonly Color[] blueGradientCache = new Color[101]; // 0.0 to 1.0 in 0.01 increments
        private bool needsFullRedraw = true;
        private int renderHeight = 200; // Track actual rendering height

        public WaterfallPanel()
        {
            InitializeComponent();

            // Initialize waterfall columns
            for (int i = 0; i < WaterfallWidth; i++)
            {
                waterfallColumns.Add(new WaterfallColumn());
            }

            // Initialize color caches
            InitializeBlueGradientCache();
            InitializeBrushCache();

            // Set initial render height
            UpdateRenderHeight();
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            UpdateRenderHeight();
            needsFullRedraw = true;
        }

        private void UpdateRenderHeight()
        {
            renderHeight = Math.Max(Height, ClientSize.Height);
            if (renderHeight <= 0) renderHeight = 200; // Fallback to expected height
        }

        private void InitializeBlueGradientCache()
        {
            // Create blue gradient from light blue to dark blue
            for (int i = 0; i <= 100; i++)
            {
                float intensity = i / 100f;
                int r = (int)(173 - (53 * intensity));  // 173 -> 120
                int g = (int)(216 - (66 * intensity));  // 216 -> 150  
                int b = (int)(255 - (135 * intensity)); // 255 -> 120
                blueGradientCache[i] = Color.FromArgb(255, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
            }
        }

        private void InitializeBrushCache()
        {
            // Pre-create brushes for different colors
            for (int i = 0; i < 256; i++)
            {
                colorBrushCache[i] = new SolidBrush(Color.FromArgb(i, 100, 150, 255));
            }
        }

        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            // Check if disposed to prevent errors during shutdown
            if (isDisposed || IsDisposed)
                return;

            // Scroll the waterfall downward
            scrollOffset += ScrollSpeed;
            if (scrollOffset >= renderHeight + MessageHeight)
            {
                scrollOffset = 0;
            }

            // Age all messages and remove old ones
            bool hasChanges = false;
            foreach (var column in waterfallColumns)
            {
                for (int i = column.Messages.Count - 1; i >= 0; i--)
                {
                    var msg = column.Messages[i];
                    msg.Y += ScrollSpeed; // Move downward

                    // Remove messages that have scrolled off screen (bottom)
                    if (msg.Y > renderHeight)
                    {
                        column.Messages.RemoveAt(i);
                        hasChanges = true;
                    }
                }
            }

            if (hasChanges || needsFullRedraw)
            {
                Invalidate();
                needsFullRedraw = false;
            }
        }

        public void BlipMessage(string frameId)
        {
            if (isDisposed || IsDisposed)
                return;

            // Get or create color for this frame ID
            if (!frameIdColors.ContainsKey(frameId))
            {
                // Generate a blue-tinted color for consistency with other panels
                float hue = (frameId.GetHashCode() % 360 + 360) % 360;
                Color baseColor = ColorFromHSV(hue, 0.7f, 0.9f);

                // Tint towards blue to match other panels
                int r = (baseColor.R + 100) / 2;
                int g = (baseColor.G + 150) / 2;
                int b = Math.Min(255, (baseColor.B + 255) / 2);

                frameIdColors[frameId] = Color.FromArgb(255, r, g, b);
            }

            // Find the best column to add this message (least crowded)
            int bestColumn = 0;
            int minMessages = int.MaxValue;

            for (int i = 0; i < waterfallColumns.Count; i++)
            {
                int messageCount = waterfallColumns[i].Messages.Count;
                if (messageCount < minMessages)
                {
                    minMessages = messageCount;
                    bestColumn = i;
                }
            }

            // Add message to the selected column at the top
            var message = new WaterfallMessage
            {
                FrameId = frameId,
                Color = frameIdColors[frameId],
                Y = -MessageHeight, // Start from top edge (above visible area)
                Intensity = 1.0f,
                CreationTime = Environment.TickCount
            };

            waterfallColumns[bestColumn].Messages.Add(message);
            needsFullRedraw = true;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (isDisposed || IsDisposed)
                return;

            base.OnPaint(e);

            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.CompositingQuality = CompositingQuality.HighSpeed;

            // Clear background
            g.Clear(BackColor);

            // Debug: Draw a border to show the full control area
            using (var borderPen = new Pen(Color.FromArgb(50, Color.White), 1))
            {
                g.DrawRectangle(borderPen, 0, 0, Width - 1, renderHeight - 1);
            }

            // Draw waterfall columns
            for (int columnIndex = 0; columnIndex < waterfallColumns.Count; columnIndex++)
            {
                int x = columnIndex * ColumnWidth;
                if (x >= Width) break; // Don't draw beyond control width

                var column = waterfallColumns[columnIndex];

                foreach (var message in column.Messages)
                {
                    // Only draw if message is within visible area
                    if (message.Y >= -MessageHeight && message.Y <= renderHeight)
                    {
                        // Calculate fade based on age
                        int age = Environment.TickCount - message.CreationTime;
                        float fade = Math.Max(0f, 1f - (age / 5000f)); // Fade over 5 seconds

                        if (fade > 0)
                        {
                            // Create faded color
                            int alpha = (int)(255 * fade * message.Intensity);
                            Color fadedColor = Color.FromArgb(alpha, message.Color);

                            // Draw the message block
                            using (var brush = new SolidBrush(fadedColor))
                            {
                                g.FillRectangle(brush, x, message.Y, ColumnWidth, MessageHeight);
                            }
                        }
                    }
                }
            }

            // Draw subtle grid lines for better visibility
            using (var gridPen = new Pen(Color.FromArgb(30, Color.White), 1))
            {
                // Vertical lines every 20 columns
                for (int i = 0; i < WaterfallWidth; i += 20)
                {
                    int x = i * ColumnWidth;
                    if (x < Width)
                    {
                        g.DrawLine(gridPen, x, 0, x, renderHeight);
                    }
                }
            }
        }

        private Color ColorFromHSV(float hue, float saturation, float value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            float f = (float)(hue / 60 - Math.Floor(hue / 60));

            value = value * 255;
            int v = Convert.ToInt32(value);
            int p = Convert.ToInt32(value * (1 - saturation));
            int q = Convert.ToInt32(value * (1 - f * saturation));
            int t = Convert.ToInt32(value * (1 - (1 - f) * saturation));

            if (hi == 0)
                return Color.FromArgb(255, v, t, p);
            else if (hi == 1)
                return Color.FromArgb(255, q, v, p);
            else if (hi == 2)
                return Color.FromArgb(255, p, v, t);
            else if (hi == 3)
                return Color.FromArgb(255, p, q, v);
            else if (hi == 4)
                return Color.FromArgb(255, t, p, v);
            else
                return Color.FromArgb(255, v, p, q);
        }

        private class WaterfallColumn
        {
            public List<WaterfallMessage> Messages { get; } = new List<WaterfallMessage>();
        }

        private class WaterfallMessage
        {
            public string FrameId { get; set; }
            public Color Color { get; set; }
            public int Y { get; set; }
            public float Intensity { get; set; }
            public int CreationTime { get; set; }
        }
    }
}
