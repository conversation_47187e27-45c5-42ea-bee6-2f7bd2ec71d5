using System.ComponentModel;

partial class PixelGridPanel
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up cached brushes
            for (int i = 0; i < brushCache.Length; i++)
            {
                brushCache[i]?.Dispose();
            }
            updateTimer?.Dispose();
            
            if (components != null)
            {
                components.Dispose();
            }
        }
        base.Dispose(disposing);
    }

    #region Component Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.components = new System.ComponentModel.Container();
        this.updateTimer = new System.Windows.Forms.Timer(this.components);
        this.SuspendLayout();
        // 
        // updateTimer
        // 
        this.updateTimer.Interval = 33;
        this.updateTimer.Tick += new System.EventHandler(this.UpdateTimer_Tick);
        //
        // PixelGridPanel
        //
        this.BackColor = System.Drawing.Color.Black;
        this.DoubleBuffered = true;
        this.Size = new System.Drawing.Size(5 * (14 + 3), 70 * (14 + 3)); // GridWidth * (BlockSize + Gap), GridHeight * (BlockSize + Gap)
        this.SetStyle(System.Windows.Forms.ControlStyles.AllPaintingInWmPaint |
                     System.Windows.Forms.ControlStyles.UserPaint |
                     System.Windows.Forms.ControlStyles.OptimizedDoubleBuffer, true);
        this.ResumeLayout(false);
    }

    #endregion

    private System.Windows.Forms.Timer updateTimer;
}
