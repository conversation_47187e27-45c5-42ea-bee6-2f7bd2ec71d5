using System.ComponentModel;

partial class PixelGridPanel
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && !isDisposed)
        {
            isDisposed = true;

            // Stop timer first to prevent further updates
            updateTimer?.Stop();

            // Clean up cached brushes safely
            if (brushCache != null)
            {
                for (int i = 0; i < brushCache.Length; i++)
                {
                    if (brushCache[i] != null)
                    {
                        brushCache[i].Dispose();
                        brushCache[i] = null;
                    }
                }
            }

            // Dispose timer
            updateTimer?.Dispose();

            // Dispose components
            if (components != null)
            {
                components.Dispose();
            }
        }
        base.Dispose(disposing);
    }

    #region Component Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.components = new System.ComponentModel.Container();
        this.updateTimer = new System.Windows.Forms.Timer(this.components);
        this.SuspendLayout();
        // 
        // updateTimer
        // 
        this.updateTimer.Interval = 33;
        this.updateTimer.Tick += new System.EventHandler(this.UpdateTimer_Tick);
        //
        // PixelGridPanel
        //
        this.BackColor = System.Drawing.Color.Black;
        this.DoubleBuffered = true;
        this.Size = new System.Drawing.Size(5 * (6 + 2), 70 * (6 + 2)); // GridWidth * (BlockSize + Gap), GridHeight * (BlockSize + Gap)
        this.SetStyle(System.Windows.Forms.ControlStyles.AllPaintingInWmPaint |
                     System.Windows.Forms.ControlStyles.UserPaint |
                     System.Windows.Forms.ControlStyles.OptimizedDoubleBuffer, true);
        this.ResumeLayout(false);
    }

    #endregion

    private System.Windows.Forms.Timer updateTimer;
}
