using System.ComponentModel;

partial class SignalstrandPanel
{
    /// <summary>
    /// Required designer variable.
    /// </summary>
    private IContainer components = null;

    /// <summary>
    /// Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            refreshTimer?.Dispose();
            
            if (components != null)
            {
                components.Dispose();
            }
        }
        base.Dispose(disposing);
    }

    #region Component Designer generated code

    /// <summary>
    /// Required method for Designer support - do not modify
    /// the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.components = new System.ComponentModel.Container();
        this.refreshTimer = new System.Windows.Forms.Timer(this.components);
        this.SuspendLayout();
        //
        // refreshTimer
        //
        this.refreshTimer.Interval = 45;
        this.refreshTimer.Tick += new System.EventHandler(this.RefreshTimer_Tick);
        //
        // SignalstrandPanel
        //
        this.DoubleBuffered = true;
        this.Size = new System.Drawing.Size(120, 512); // 256 * 2 (MaxLines * LineSpacing)
        this.SetStyle(System.Windows.Forms.ControlStyles.OptimizedDoubleBuffer |
                     System.Windows.Forms.ControlStyles.AllPaintingInWmPaint, true);
        this.ResumeLayout(false);
    }

    #endregion

    private System.Windows.Forms.Timer refreshTimer;
}
