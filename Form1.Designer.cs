﻿namespace LiveCAN
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Stop timer first to prevent further updates
                uiUpdateTimer?.Stop();
                uiUpdateTimer?.Dispose();

                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnLoadFile = new System.Windows.Forms.Button();
            this.btnStartReplay = new System.Windows.Forms.Button();
            this.btnStopReplay = new System.Windows.Forms.Button();
            this.openFileDialog = new System.Windows.Forms.OpenFileDialog();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.statusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.dgvSummary = new System.Windows.Forms.DataGridView();
            this.chk = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.ID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.count = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.uiUpdateTimer = new System.Windows.Forms.Timer(this.components);
            this.pixelPanel = new PixelGridPanel();
            this.signalstrandPanel1 = new SignalstrandPanel();
            this.statusStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvSummary)).BeginInit();
            this.SuspendLayout();
            // 
            // btnLoadFile
            // 
            this.btnLoadFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnLoadFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnLoadFile.FlatAppearance.BorderSize = 0;
            this.btnLoadFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLoadFile.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.btnLoadFile.Location = new System.Drawing.Point(270, 7);
            this.btnLoadFile.Name = "btnLoadFile";
            this.btnLoadFile.Size = new System.Drawing.Size(92, 23);
            this.btnLoadFile.TabIndex = 0;
            this.btnLoadFile.Text = "Load File";
            this.btnLoadFile.UseVisualStyleBackColor = false;
            this.btnLoadFile.Click += new System.EventHandler(this.btnLoadFile_Click);
            // 
            // btnStartReplay
            // 
            this.btnStartReplay.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStartReplay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnStartReplay.FlatAppearance.BorderSize = 0;
            this.btnStartReplay.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStartReplay.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.btnStartReplay.Location = new System.Drawing.Point(368, 7);
            this.btnStartReplay.Name = "btnStartReplay";
            this.btnStartReplay.Size = new System.Drawing.Size(90, 23);
            this.btnStartReplay.TabIndex = 1;
            this.btnStartReplay.Text = "Start Replay";
            this.btnStartReplay.UseVisualStyleBackColor = false;
            this.btnStartReplay.Click += new System.EventHandler(this.btnStartReplay_Click);
            // 
            // btnStopReplay
            // 
            this.btnStopReplay.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStopReplay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnStopReplay.FlatAppearance.BorderSize = 0;
            this.btnStopReplay.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnStopReplay.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.btnStopReplay.Location = new System.Drawing.Point(464, 7);
            this.btnStopReplay.Name = "btnStopReplay";
            this.btnStopReplay.Size = new System.Drawing.Size(95, 23);
            this.btnStopReplay.TabIndex = 2;
            this.btnStopReplay.Text = "Stop Replay";
            this.btnStopReplay.UseVisualStyleBackColor = false;
            this.btnStopReplay.Click += new System.EventHandler(this.btnStopReplay_Click);
            // 
            // openFileDialog
            // 
            this.openFileDialog.FileName = "openFileDialog1";
            // 
            // statusStrip
            // 
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.statusLabel});
            this.statusStrip.Location = new System.Drawing.Point(0, 543);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(565, 22);
            this.statusStrip.TabIndex = 3;
            this.statusStrip.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new System.Drawing.Size(39, 17);
            this.statusLabel.Text = "Ready";
            // 
            // dgvSummary
            // 
            this.dgvSummary.AllowUserToAddRows = false;
            this.dgvSummary.AllowUserToDeleteRows = false;
            this.dgvSummary.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvSummary.BackgroundColor = System.Drawing.SystemColors.ControlDark;
            this.dgvSummary.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvSummary.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.chk,
            this.ID,
            this.count});
            this.dgvSummary.Location = new System.Drawing.Point(122, 36);
            this.dgvSummary.Name = "dgvSummary";
            this.dgvSummary.ReadOnly = true;
            this.dgvSummary.RowHeadersVisible = false;
            this.dgvSummary.Size = new System.Drawing.Size(443, 507);
            this.dgvSummary.TabIndex = 4;
            // 
            // chk
            // 
            this.chk.FillWeight = 30.45329F;
            this.chk.HeaderText = "";
            this.chk.MinimumWidth = 20;
            this.chk.Name = "chk";
            this.chk.ReadOnly = true;
            this.chk.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            //
            // ID
            // 
            this.ID.FillWeight = 72.35435F;
            this.ID.HeaderText = "ID";
            this.ID.MinimumWidth = 60;
            this.ID.Name = "ID";
            this.ID.ReadOnly = true;
            // 
            // count
            // 
            this.count.FillWeight = 134.7558F;
            this.count.HeaderText = "Count";
            this.count.Name = "count";
            this.count.ReadOnly = true;
            //
            // uiUpdateTimer
            //
            this.uiUpdateTimer.Interval = 500;
            this.uiUpdateTimer.Tick += new System.EventHandler(this.UiUpdateTimer_Tick);
            // 
            // pixelPanel
            // 
            this.pixelPanel.BackColor = System.Drawing.SystemColors.ControlDark;
            this.pixelPanel.Location = new System.Drawing.Point(0, 36);
            this.pixelPanel.Name = "pixelPanel";
            this.pixelPanel.Size = new System.Drawing.Size(123, 210);
            this.pixelPanel.TabIndex = 5;
            //
            // signalstrandPanel1
            //
            this.signalstrandPanel1.BackColor = System.Drawing.SystemColors.ControlDark;
            this.signalstrandPanel1.ForeColor = System.Drawing.SystemColors.ControlText;
            this.signalstrandPanel1.Location = new System.Drawing.Point(0, 245);
            this.signalstrandPanel1.Name = "signalstrandPanel1";
            this.signalstrandPanel1.Size = new System.Drawing.Size(124, 299);
            this.signalstrandPanel1.TabIndex = 6;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.ControlDark;
            this.ClientSize = new System.Drawing.Size(565, 565);
            this.Controls.Add(this.signalstrandPanel1);
            this.Controls.Add(this.pixelPanel);
            this.Controls.Add(this.dgvSummary);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.btnStopReplay);
            this.Controls.Add(this.btnStartReplay);
            this.Controls.Add(this.btnLoadFile);
            this.DoubleBuffered = true;
            this.Name = "Form1";
            this.Text = "CAN Log Viewer";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvSummary)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnLoadFile;
        private System.Windows.Forms.Button btnStartReplay;
        private System.Windows.Forms.Button btnStopReplay;
        private System.Windows.Forms.OpenFileDialog openFileDialog;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel statusLabel;
        private System.Windows.Forms.DataGridView dgvSummary;
        private System.Windows.Forms.DataGridViewCheckBoxColumn chk;
        private System.Windows.Forms.DataGridViewTextBoxColumn ID;
        private System.Windows.Forms.DataGridViewTextBoxColumn count;
        private System.Windows.Forms.Timer uiUpdateTimer;
        private PixelGridPanel pixelPanel;
        private SignalstrandPanel signalstrandPanel1;
    }
}
