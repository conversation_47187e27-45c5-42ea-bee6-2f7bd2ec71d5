using System;
using System.ComponentModel;
using System.Windows.Forms;

namespace LiveCAN
{
    partial class WaterfallPanel
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && !isDisposed)
            {
                isDisposed = true;
                
                // Stop timer first to prevent further updates
                updateTimer?.Stop();
                
                // Clean up cached brushes safely
                if (colorBrushCache != null)
                {
                    for (int i = 0; i < colorBrushCache.Length; i++)
                    {
                        if (colorBrushCache[i] != null)
                        {
                            colorBrushCache[i].Dispose();
                            colorBrushCache[i] = null;
                        }
                    }
                }
                
                // Dispose timer
                updateTimer?.Dispose();
                
                // Dispose components
                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.updateTimer = new System.Windows.Forms.Timer(this.components);
            this.SuspendLayout();
            // 
            // updateTimer
            // 
            this.updateTimer.Interval = 50;
            this.updateTimer.Tick += new System.EventHandler(this.UpdateTimer_Tick);
            // 
            // WaterfallPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Black;
            this.DoubleBuffered = true;
            this.Name = "WaterfallPanel";
            this.Size = new System.Drawing.Size(400, 200);
            this.ResumeLayout(false);
            
            // Start the timer
            this.updateTimer.Start();
        }

        #endregion

    }
}
