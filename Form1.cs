﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Collections.Concurrent;
using Timer = System.Windows.Forms.Timer;
using System.Text;

namespace LiveCAN
{
    public partial class Form1 : Form
    {
        private List<CanMessage> messages;
        private CancellationTokenSource cancellationTokenSource;
        private bool isReplaying = false;
        private bool uiUpdateProcessing;
        private ConcurrentDictionary<string, MessageStats> messageStats = new ConcurrentDictionary<string, MessageStats>();
        private static readonly double TimerFrequency = Stopwatch.Frequency / 1000.0;
        private SignalstrandPanel signalPanel;

        private Dictionary<string, DataGridViewRow> frameIdRowMap = new Dictionary<string, DataGridViewRow>();


        public Form1()
        {
            InitializeComponent();

            // Configure DataGridView columns programmatically
            dgvSummary.AutoGenerateColumns = false;
            dgvSummary.Columns.Clear();
            dgvSummary.Columns.Add(new DataGridViewCheckBoxColumn { Name = "chk", HeaderText = "", FillWeight = 30.45329F, DividerWidth = 1, MinimumWidth = 20, Resizable = System.Windows.Forms.DataGridViewTriState.False });
            dgvSummary.Columns.Add(new DataGridViewTextBoxColumn { Name = "tStamp", HeaderText = "Last Offset", FillWeight = 162.4366F, MinimumWidth = 80 });
            dgvSummary.Columns.Add(new DataGridViewTextBoxColumn { Name = "ID", HeaderText = "ID", FillWeight = 72.35435F, MinimumWidth = 60 });
            dgvSummary.Columns.Add(new DataGridViewTextBoxColumn { Name = "count", HeaderText = "Count", FillWeight = 134.7558F });

            // Start the UI update timer
            uiUpdateTimer.Start();

            uiUpdateProcessing = false;
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Stop all operations
            cancellationTokenSource?.Cancel();
            isReplaying = false;

            // Stop UI update timer
            uiUpdateTimer?.Stop();

            // Stop pixel panel timer
            pixelPanel?.Dispose();
        }

        private void btnLoadFile_Click(object sender, EventArgs e)
        {
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    statusLabel.Text = "Loading file...";
                   // messages = Task.Run(() => ParseLogFile(openFileDialog.FileName)).Result; 
                    messages = Task.Run(() => ParseLogFile(@"C:\Users\<USER>\OneDrive\Desktop\w205_can500_HMI.log")).Result; 

                    if (messages.Count == 0)
                    {
                        MessageBox.Show("No valid CAN messages found in the log file.",
                            "Empty File", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    //MessageBox.Show($"Loaded {messages.Count} messages.",
                       // "File Loaded", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    btnStartReplay.Enabled = true;
                    btnStopReplay.Enabled = true;
                    btnStartReplay_Click(sender, e);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading file: {ex.Message}",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnStartReplay_Click(object sender, EventArgs e)
        {
            if (messages == null || messages.Count == 0 || isReplaying)
                return;

            isReplaying = true;
            cancellationTokenSource = new CancellationTokenSource();

            messageStats.Clear();

            try
            {
                await Task.Run(() => ReplayMessagesWithHighPrecisionTiming(messages, cancellationTokenSource.Token),
                    cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
            }
            finally
            {
                //isReplaying = false;
            }
        }

        private void btnStopReplay_Click(object sender, EventArgs e)
        {
            cancellationTokenSource?.Cancel();
            isReplaying = false;
        }

        private List<CanMessage> ParseLogFile(string filePath)
        {
            var msgs = new List<CanMessage>();

            using (var reader = new StreamReader(filePath))
            {
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    if (line.Trim().StartsWith(";") || string.IsNullOrWhiteSpace(line))
                        continue;

                    try
                    {
                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 7)
                        {
                            double timeOffset = double.Parse(parts[1]);
                            string frameId = parts[3];
                            string data = string.Join(" ", parts.Skip(6));
                            msgs.Add(new CanMessage
                            {
                                TimeOffsetMs = timeOffset,
                                FrameId = frameId,
                                Data = data
                            });
                        }
                    }
                    catch
                    {
                    }
                }
            }
            return msgs;
        }

        private void ReplayMessagesWithHighPrecisionTiming(List<CanMessage> messages, CancellationToken token)
        {
            Thread.CurrentThread.Priority = ThreadPriority.Highest;
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            foreach (var msg in messages)
            {
                if (token.IsCancellationRequested)
                    token.ThrowIfCancellationRequested();

                double waitTime = msg.TimeOffsetMs - stopwatch.Elapsed.TotalMilliseconds;
                if (waitTime > 0)
                {
                    int sleepTime = (int)Math.Floor(waitTime);
                    if (sleepTime > 0)
                    {
                        Thread.Sleep(sleepTime);
                    }
                    while (stopwatch.Elapsed.TotalMilliseconds < msg.TimeOffsetMs)
                    {
                        Thread.SpinWait(10);
                    }
                }

                ProcessMessage(msg);
            }
            stopwatch.Stop();
        }

        private void ProcessMessage(CanMessage msg)
        {
            messageStats.AddOrUpdate(msg.FrameId,
         id => new MessageStats
         {
             Count = 1,
             LastTimeOffset = msg.TimeOffsetMs,
             Changes = 1,
             LastMessage = msg
         },
         (id, stats) =>
         {
             stats.Count++;
             stats.LastTimeOffset = msg.TimeOffsetMs;
             stats.Changes++;
             stats.LastMessage = msg;
             return stats;
         });
            
            Invoke(new Action(() => pixelPanel.BlipMessage(msg.FrameId)));

        }

        private void UiUpdateTimer_Tick(object sender, EventArgs e)
        {
            if (uiUpdateProcessing || !isReplaying)
                return;

            uiUpdateProcessing = true;

            try
            {
                dgvSummary.SuspendLayout();

                foreach (var kvp in messageStats)
                {
                    string frameId = kvp.Key;
                    MessageStats stats = kvp.Value;

                    if (frameIdRowMap.TryGetValue(frameId, out var row))
                    {
                        // Fast update
                        row.Cells["tStamp"].Value = stats.LastTimeOffset.ToString("F3") + "ms";
                        row.Cells["count"].Value = stats.Count;
                    }
                    else
                    {
                        // Add new row
                        int rowIndex = dgvSummary.Rows.Add();
                        row = dgvSummary.Rows[rowIndex];
                        row.Cells["chk"].Value = false;
                        row.Cells["tStamp"].Value = stats.LastTimeOffset.ToString("F3") + "ms";
                        row.Cells["ID"].Value = frameId;
                        row.Cells["count"].Value = stats.Count;
                        frameIdRowMap[frameId] = row;
                    }
                }

                dgvSummary.ResumeLayout();
            }
            finally
            {
                uiUpdateProcessing = false;
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                statusLabel.Text = "Loading file...";
                // messages = Task.Run(() => ParseLogFile(openFileDialog.FileName)).Result; 
                messages = Task.Run(() => ParseLogFile(@"C:\Users\<USER>\OneDrive\Desktop\w205_can500_HMI.log")).Result;

                if (messages.Count == 0)
                {
                    MessageBox.Show("No valid CAN messages found in the log file.",
                        "Empty File", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                //MessageBox.Show($"Loaded {messages.Count} messages.",
                // "File Loaded", MessageBoxButtons.OK, MessageBoxIcon.Information);
                btnStartReplay.Enabled = true;
                btnStopReplay.Enabled = true;
                btnStartReplay_Click(sender, e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading file: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    public class CanMessage
    {
        public double TimeOffsetMs { get; set; }
        public string FrameId { get; set; }
        public string Data { get; set; }
    }

    public class MessageStats
    {
        public int Count { get; set; }
        public double LastTimeOffset { get; set; }
        public int Changes { get; set; }
        public CanMessage LastMessage { get; set; }
    }
}
