﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

public partial class SignalstrandPanel : Panel
{
    private class LineState
    {
        public float Brightness = 0f;
        public float Heat = 0f;
        public int Length = 30;
        public long LastUpdateTime = 0;
        public long LastMessageTime = 0;
        public float Frequency = 0f; // Frequency variable to control the length and color
    }

    private readonly Dictionary<string, int> idToRow = new Dictionary<string, int>();
    private readonly List<LineState> rows = new List<LineState>();
    private int nextRow = 0;

    private const int LineHeight = 3;
    private const int LineSpacing = 2;
    private const int MaxLines = 256;

    public SignalstrandPanel()
    {
        InitializeComponent();

        for (int i = 0; i < MaxLines; i++)
            rows.Add(new LineState());

        refreshTimer.Start();
    }

    public void BlipMessage(string frameId)
    {
        if (!idToRow.ContainsKey(frameId))
        {
            if (nextRow >= MaxLines)
                return; // Out of space
            idToRow[frameId] = nextRow++;
        }

        int row = idToRow[frameId];
        LineState state = rows[row];

        long now = Environment.TickCount;

        // Calculate frequency based on time between messages
        if (state.LastMessageTime > 0)
        {
            long messageInterval = now - state.LastMessageTime;
            state.Frequency = Math.Min(5.0f, (float)(1000.0 / messageInterval)); // Frequency: messages per second
        }

        state.LastMessageTime = now;

        state.Brightness = 1.0f;
        state.Heat = Math.Min(1.0f, state.Heat + 0.05f);
        state.Length = 30 * (int)(state.Frequency / state.Heat)/3; // Length influenced by frequency
        state.LastUpdateTime = now;
    }

    private void RefreshTimer_Tick(object sender, EventArgs e)
    {
        Invalidate();
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        Graphics g = e.Graphics;
        g.SmoothingMode = SmoothingMode.HighQuality;
        g.Clear(Color.Black);

        long now = Environment.TickCount;

        for (int i = 0; i < nextRow; i++)
        {
            LineState state = rows[i];

            // Update fading
            float elapsed = (now - state.LastUpdateTime) / 1000f;
            state.Brightness = Math.Max(0f, state.Brightness - elapsed * 2.5f);
            state.Heat = Math.Max(0f, state.Heat - elapsed * 0.7f);

            if (state.Brightness <= 0.01f)
                continue;

            // Color based on frequency and heat
            Color baseColor = ColorFromFrequency(state.Frequency, state.Heat);
            int alpha = (int)(255 * state.Brightness);
            using (SolidBrush brush = new SolidBrush(Color.FromArgb(alpha, baseColor)))
            {
                int y = i * LineSpacing;
                Rectangle rect = new Rectangle(5, y, state.Length, LineHeight);
                g.FillRectangle(brush, rect);
            }
        }
    }

    private Color ColorFromFrequency(float frequency, float heat)
    {
        // Adjust color intensity based on frequency
        heat = Math.Max(0f, Math.Min(1f, heat));
        if (frequency > 1.3f)
            return Color.FromArgb(255, (int)(255 * heat), 0); // High frequency = bright color (e.g., red)
        else if (frequency > 0.9f)
            return Color.FromArgb(0, (int)(255 * heat), 0); // Medium frequency = green
        else
            return Color.FromArgb(0, 0, (int)(255 * heat)); // Low frequency = blue
    }
}
