﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

public partial class PixelGridPanel : Panel
{
    private const int GridWidth = 5;
    private const int GridHeight = 70;
    private const int BlockSize = 6;
    private const int Gap = 2;
    private readonly bool enableSubtleEffects = true;

    private class PixelState
    {
        public float Intensity = 0f;
        public float Pulse = 0f;
        public float WavePhase = 0f;
        public float Hotness = 0f; // 0.0 = light blue, 1.0 = dark blue
        public int MessageCount = 0; // Track how many messages this pixel has received
        public int LastActivationTime = 0;
        public bool NeedsRedraw = false; // Track if this pixel needs to be redrawn
    }

    private readonly PixelState[,] pixelStates = new PixelState[GridWidth, GridHeight];
    private readonly Random random = new Random();
    private float globalTime = 0f;
    private readonly Dictionary<string, int> frameIdToPixel = new Dictionary<string, int>(); // Map frame IDs to specific pixels

    // Performance optimizations - pre-calculated values and object pooling
    private readonly Color[] hotnessColorCache = new Color[101]; // Cache colors for hotness 0.0 to 1.0 in 0.01 increments
    private readonly SolidBrush[] brushCache = new SolidBrush[256]; // Cache brushes for different alpha values
    private bool needsFullRedraw = true;
    private int lastUpdateTime = 0;
    private bool isDisposed = false;

    public PixelGridPanel()
    {
        InitializeComponent();

        // Initialize pixel states
        for (int x = 0; x < GridWidth; x++)
        {
            for (int y = 0; y < GridHeight; y++)
            {
                var state = new PixelState();
                state.WavePhase = (float)random.NextDouble() * (float)Math.PI * 2;
                pixelStates[x, y] = state;
            }
        }

        // Pre-calculate color cache for performance
        InitializeColorCache();
        InitializeBrushCache();

        updateTimer.Start();
    }

    private void UpdateTimer_Tick(object sender, EventArgs e)
    {
        // Check if disposed to prevent errors during shutdown
        if (isDisposed || IsDisposed)
            return;

        if (UpdateStates()) // Only invalidate if something changed
        {
            Invalidate();
        }
    }

    public void BlipMessage(string frameId)
    {
        // Get or assign a consistent pixel for this frame ID
        int pixelIndex;
        if (!frameIdToPixel.TryGetValue(frameId, out pixelIndex))
        {
            pixelIndex = frameIdToPixel.Count % (GridWidth * GridHeight);
            frameIdToPixel[frameId] = pixelIndex;
        }

        int x = pixelIndex % GridWidth;
        int y = pixelIndex / GridWidth;
        var pixel = pixelStates[x, y];

        pixel.MessageCount++;
        int currentTime = Environment.TickCount;

        // Only activate pixel if it meets certain criteria for being "hot"
        bool shouldActivate = false;

        // Criteria 1: High frequency messages (short time between messages)
        if (pixel.LastActivationTime > 0)
        {
            int timeSinceLastActivation = currentTime - pixel.LastActivationTime;
            if (timeSinceLastActivation < 100) // Less than 100ms = high frequency
            {
                shouldActivate = true;
            }
        }

        // Criteria 2: Messages that have been seen multiple times recently
        if (pixel.MessageCount > 5 && (pixel.MessageCount % 3 == 0))
        {
            shouldActivate = true;
        }

        // Criteria 3: Random chance for occasional activation (much lower)
        if (random.NextDouble() < 0.05) // Only 5% chance instead of 100%
        {
            shouldActivate = true;
        }

        if (shouldActivate)
        {
            pixel.Intensity = 1f;
            pixel.Pulse = 1f;

            // Calculate hotness based on frequency and message count
            float frequencyHotness = GetHotnessForFrequency(currentTime - pixel.LastActivationTime);
            float countHotness = Math.Min(1f, pixel.MessageCount / 20f); // Max hotness at 20 messages
            pixel.Hotness = Math.Min(1f, Math.Max(frequencyHotness, countHotness));

            pixel.LastActivationTime = currentTime;
            pixel.NeedsRedraw = true; // Mark for redraw
        }
        else
        {
            // Just update the last activation time for frequency tracking
            pixel.LastActivationTime = currentTime;
        }
    }

    private bool UpdateStates()
    {
        int currentTime = Environment.TickCount;

        // Throttle updates to reduce CPU usage
        if (currentTime - lastUpdateTime < 16) // ~60 FPS max
            return false;

        lastUpdateTime = currentTime;
        globalTime += 0.02f;
        bool anyChanges = false;

        for (int x = 0; x < GridWidth; x++)
        {
            for (int y = 0; y < GridHeight; y++)
            {
                var state = pixelStates[x, y];
                bool pixelChanged = false;

                // Update wave phase for subtle animation (only if effects enabled and pixel active)
                if (enableSubtleEffects && (state.Intensity > 0 || state.Pulse > 0))
                {
                    state.WavePhase += 0.05f;
                    pixelChanged = true;
                }

                // Simple decay
                if (state.Intensity > 0.01f) // Skip very small values
                {
                    state.Intensity -= 0.02f;
                    state.Intensity = Math.Max(0f, state.Intensity);
                    pixelChanged = true;
                }

                if (state.Pulse > 0.01f) // Skip very small values
                {
                    state.Pulse -= 0.03f;
                    state.Pulse = Math.Max(0f, state.Pulse);
                    pixelChanged = true;
                }

                // Gradually decay message count and hotness to prevent permanent "hot" pixels
                if (state.LastActivationTime > 0 && currentTime - state.LastActivationTime > 2000) // 2 seconds
                {
                    if (state.MessageCount > 0)
                    {
                        state.MessageCount = Math.Max(0, state.MessageCount - 1);
                        pixelChanged = true;
                    }

                    // Gradually cool down the hotness
                    if (state.Hotness > 0.01f)
                    {
                        state.Hotness -= 0.01f;
                        state.Hotness = Math.Max(0f, state.Hotness);
                        pixelChanged = true;
                    }
                }

                if (pixelChanged)
                {
                    state.NeedsRedraw = true;
                    anyChanges = true;
                }
            }
        }

        return anyChanges;
    }

    protected override void OnPaint(PaintEventArgs e)
    {
        Graphics g = e.Graphics;

        // Only use anti-aliasing if we have active pixels (performance optimization)
        bool hasActivePixels = false;
        for (int x = 0; x < GridWidth && !hasActivePixels; x++)
        {
            for (int y = 0; y < GridHeight && !hasActivePixels; y++)
            {
                if (pixelStates[x, y].Intensity > 0 || pixelStates[x, y].Pulse > 0)
                    hasActivePixels = true;
            }
        }

        if (hasActivePixels)
            g.SmoothingMode = SmoothingMode.AntiAlias;

        for (int x = 0; x < GridWidth; x++)
        {
            for (int y = 0; y < GridHeight; y++)
            {
                var state = pixelStates[x, y];

                // Skip completely inactive pixels for performance
                float totalIntensity = Math.Max(state.Intensity, state.Pulse * 0.5f);
                if (totalIntensity <= 0.01f && !needsFullRedraw && !state.NeedsRedraw)
                    continue;

                int pixelX = x * (BlockSize + Gap);
                int pixelY = y * (BlockSize + Gap);
                Rectangle rect = new Rectangle(pixelX, pixelY, BlockSize, BlockSize);

                // Use cached color for performance
                Color baseColor = GetCachedBlueGradientColor(state.Hotness);

                // Apply subtle plasma effect if enabled (simplified calculation)
                if (enableSubtleEffects && totalIntensity > 0)
                {
                    float plasma = (float)(Math.Sin(x * 0.2f + globalTime) + Math.Sin(y * 0.15f + globalTime * 0.8f)) * 0.1f + 0.9f;
                    baseColor = Color.FromArgb(baseColor.A,
                        Math.Min(255, (int)(baseColor.R * plasma)),
                        Math.Min(255, (int)(baseColor.G * plasma)),
                        Math.Min(255, (int)(baseColor.B * plasma)));

                    // Add subtle wave effect
                    float wave = (float)Math.Sin(globalTime * 3f + state.WavePhase) * 0.2f + 0.8f;
                    totalIntensity *= wave;
                }

                int alpha = (int)(255 * Math.Min(1f, totalIntensity));

                // Use cached brush for performance
                SolidBrush brush = GetCachedBrush(alpha, baseColor);
                g.FillRectangle(brush, rect);

                state.NeedsRedraw = false; // Mark as drawn
            }
        }

        needsFullRedraw = false;
    }

    private void InitializeColorCache()
    {
        // Pre-calculate all possible hotness colors for performance
        for (int i = 0; i <= 100; i++)
        {
            float hotness = i / 100f;
            int r = (int)(100 - (80 * hotness));   // 100 -> 20
            int g = (int)(150 - (110 * hotness));  // 150 -> 40
            int b = (int)(255 - (135 * hotness));  // 255 -> 120
            hotnessColorCache[i] = Color.FromArgb(255, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
        }
    }

    private void InitializeBrushCache()
    {
        // Pre-create brushes for different alpha values
        for (int i = 0; i < 256; i++)
        {
            brushCache[i] = new SolidBrush(Color.FromArgb(i, 100, 150, 255));
        }
    }

    private Color GetCachedBlueGradientColor(float hotness)
    {
        // Use cached color for performance
        int index = Math.Min(100, Math.Max(0, (int)(hotness * 100)));
        return hotnessColorCache[index];
    }

    private SolidBrush GetCachedBrush(int alpha, Color baseColor)
    {
        // Reuse existing brush if possible, or update it
        alpha = Math.Min(255, Math.Max(0, alpha));
        var brush = brushCache[alpha];

        // Update brush color if needed (more efficient than creating new brush)
        Color newColor = Color.FromArgb(alpha, baseColor.R, baseColor.G, baseColor.B);
        if (brush != null && brush.Color != newColor)
        {
            brush.Dispose();
            brushCache[alpha] = new SolidBrush(newColor);
            return brushCache[alpha];
        }

        // If brush is null, create a new one
        if (brush == null)
        {
            brushCache[alpha] = new SolidBrush(newColor);
            return brushCache[alpha];
        }

        return brush;
    }

    private Color GetBlueGradientColor(float hotness)
    {
        // Fallback method (now uses cache)
        return GetCachedBlueGradientColor(hotness);
    }

    private float GetHotnessForFrequency(int timeSinceLastMessage)
    {
        if (timeSinceLastMessage < 50)       // Very high frequency = max hotness
            return 1.0f;
        else if (timeSinceLastMessage < 100) // High frequency = high hotness
            return 0.8f;
        else if (timeSinceLastMessage < 200) // Medium frequency = medium hotness
            return 0.5f;
        else if (timeSinceLastMessage < 500) // Low frequency = low hotness
            return 0.2f;
        else                                 // Very low frequency = minimal hotness
            return 0.1f;
    }

}
